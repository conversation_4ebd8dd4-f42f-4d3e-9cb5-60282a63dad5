import { authRouter } from '@/router/admin/auth'
import { adminQuotaRouter } from '@/router/admin/quota'
import { router, t } from '@/trpc'
import { orderRouter } from './order'
import { userRouter } from './user'
import { applicationRouter } from './application'

export const appRouter = router({
  auth: authRouter,
  quota: adminQuotaRouter,
  order: orderRouter,
  user: userRouter,
  application: applicationRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter

export const createCaller = t.createCallerFactory(appRouter)
