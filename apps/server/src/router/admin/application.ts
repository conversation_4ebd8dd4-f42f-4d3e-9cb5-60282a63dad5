import { router } from '@/trpc'
import { ApplicationListSchema, type Prisma } from '@coozf/db'
import { adminProtectedProcedure, applicationProcedure, applicationWithBalanceProcedure } from '@/procedure'

export const applicationRouter = router({
  // 获取应用列表（分页）
  list: adminProtectedProcedure.input(ApplicationListSchema).query(async ({ ctx, input }) => {
    const { page, pageSize, search } = input
    const skip = (page - 1) * pageSize

    // 构建查询条件
    const where: Prisma.ApplicationWhereInput = {
      userId: ctx.user.id,
    }

    if (search) {
      where.name = { contains: search }
    }

    // 获取总数
    const total = await ctx.repo.appRepo.count(where)

    // 获取分页数据，包含余额信息
    const applications = await ctx.db.application.findMany({
      where,
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' },
      include: {
        applicationBalance: true,
      },
    })

    // 格式化数据，隐藏敏感信息
    const formattedItems = applications.map((app) => ({
      ...app,
      secret: '****************************', // 隐藏 Secret
      webhookSecret: '****************************', // 隐藏 webhook Secret
      balance: app.applicationBalance?.balance.toNumber() ?? 0,
    }))

    return {
      items: formattedItems,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  }),

  // 获取单个应用详情
  byId: applicationWithBalanceProcedure.query(async ({ ctx }) => {
    return ctx.applicationWithBalance
  }),

  // 获取应用统计数据
  getApplicationStats: applicationProcedure.query(async ({ ctx }) => {
    // 获取API调用统计
    const apiCalls = await ctx.repo.apiCallRepo.findByApplicationId(ctx.applicationId)

    let accountQuotaCount = 0
    let trafficGB = 0

    apiCalls.forEach((call) => {
      if (call.costType === 'ACCOUNT_QUOTA') {
        accountQuotaCount += 1
      }
      if (call.costType === 'TRAFFIC') {
        trafficGB += Number(call.costAmount)
      }
    })

    // 获取充值总额
    const transactions = await ctx.repo.transactionRepo.findByApplicationId(ctx.applicationId, {
      type: 'RECHARGE',
    })

    const totalRecharge = transactions.reduce((sum, transaction) => {
      return sum + Number(transaction.amount)
    }, 0)

    return {
      apiCallCount: apiCalls.length,
      accountCount: accountQuotaCount,
      trafficGB,
      totalRecharge,
    }
  }),
})
