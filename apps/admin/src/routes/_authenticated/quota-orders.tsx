import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@coozf/ui/components/table'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@coozf/ui/components/dropdown-menu'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@coozf/ui/components/dialog'
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@coozf/ui/components/form'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@coozf/ui/components/select'
import { 
  Search, 
  MoreHorizontal, 
  Download,
  Eye,
  RefreshCw,
  Filter,
  Plus,
  Calendar,
  Users,
  HardDrive,
} from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'sonner'
import { trpc } from '@/lib/trpc'

export const Route = createFileRoute('/_authenticated/quota-orders')({
  component: QuotaOrdersPage,
})

// 创建配额订单的表单schema
const createQuotaOrderSchema = z.object({
  applicationId: z.string().min(1, '请选择应用'),
  quotaType: z.enum(['ACCOUNT', 'TRAFFIC'], { required_error: '请选择配额类型' }),
  accountCount: z.number().min(1, '账号数量必须大于0').optional(),
  trafficGB: z.number().min(1, '流量必须大于0').optional(),
  expireDate: z.string().optional(),
  remarks: z.string().optional(),
})

type CreateQuotaOrderForm = z.infer<typeof createQuotaOrderSchema>

function QuotaOrdersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [quotaTypeFilter, setQuotaTypeFilter] = useState("全部")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  // 获取配额订单列表
  const { data: ordersData, isLoading, refetch } = trpc.quota.getAllQuotaOrders.useQuery({
    page: 1,
    pageSize: 20,
  })

  // 创建账号配额订单
  const createAccountQuotaMutation = trpc.quota.createAccountQuotaOrder.useMutation({
    onSuccess: () => {
      toast.success('账号配额订单创建成功')
      setIsCreateDialogOpen(false)
      refetch()
    },
    onError: (error) => {
      toast.error(error.message || '创建失败')
    },
  })

  // 创建流量配额订单
  const createTrafficQuotaMutation = trpc.quota.createTrafficQuotaOrder.useMutation({
    onSuccess: () => {
      toast.success('流量配额订单创建成功')
      setIsCreateDialogOpen(false)
      refetch()
    },
    onError: (error) => {
      toast.error(error.message || '创建失败')
    },
  })

  // 创建订单表单
  const createForm = useForm<CreateQuotaOrderForm>({
    resolver: zodResolver(createQuotaOrderSchema),
    defaultValues: {
      applicationId: '',
      quotaType: 'ACCOUNT',
      remarks: '',
    },
  })

  const quotaType = createForm.watch('quotaType')

  // 模拟订单数据
  const mockOrders = [
    {
      id: 'QO-001',
      orderNo: 'QO-2024-001',
      applicationId: 'app-001',
      applicationName: '小红书爬虫应用',
      quotaType: 'ACCOUNT',
      quotaAmount: 50,
      createdAt: new Date('2024-01-15'),
      status: 'COMPLETED',
      remarks: '增加账号配额',
    },
    {
      id: 'QO-002',
      orderNo: 'QO-2024-002',
      applicationId: 'app-002',
      applicationName: '抖音数据分析',
      quotaType: 'TRAFFIC',
      quotaAmount: 100,
      createdAt: new Date('2024-01-14'),
      status: 'COMPLETED',
      remarks: '增加流量配额',
    },
  ]

  const orders = ordersData?.orders || mockOrders

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.applicationName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = quotaTypeFilter === "全部" || order.quotaType === quotaTypeFilter
    return matchesSearch && matchesType
  })

  const onCreateSubmit = (values: CreateQuotaOrderForm) => {
    if (values.quotaType === 'ACCOUNT') {
      createAccountQuotaMutation.mutate({
        applicationId: values.applicationId,
        accountCount: values.accountCount!,
        expireDate: values.expireDate ? new Date(values.expireDate) : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        remarks: values.remarks,
      })
    } else {
      createTrafficQuotaMutation.mutate({
        applicationId: values.applicationId,
        trafficGB: values.trafficGB!,
        remarks: values.remarks,
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">配额订单管理</h2>
          <p className="text-muted-foreground">
            查看和管理所有配额订单
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建配额订单
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>创建配额订单</DialogTitle>
                <DialogDescription>
                  为应用创建新的配额订单
                </DialogDescription>
              </DialogHeader>
              <Form {...createForm}>
                <form onSubmit={createForm.handleSubmit(onCreateSubmit)} className="space-y-4">
                  <FormField
                    control={createForm.control}
                    name="applicationId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>应用</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择应用" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="app-001">小红书爬虫应用</SelectItem>
                            <SelectItem value="app-002">抖音数据分析</SelectItem>
                            <SelectItem value="app-003">B站内容监控</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={createForm.control}
                    name="quotaType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>配额类型</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择配额类型" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="ACCOUNT">账号配额</SelectItem>
                            <SelectItem value="TRAFFIC">流量配额</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {quotaType === 'ACCOUNT' && (
                    <>
                      <FormField
                        control={createForm.control}
                        name="accountCount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>账号数量</FormLabel>
                            <FormControl>
                              <Input 
                                type="number" 
                                placeholder="输入账号数量" 
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={createForm.control}
                        name="expireDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>到期时间</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {quotaType === 'TRAFFIC' && (
                    <FormField
                      control={createForm.control}
                      name="trafficGB"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>流量 (GB)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              placeholder="输入流量大小" 
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <FormField
                    control={createForm.control}
                    name="remarks"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>备注</FormLabel>
                        <FormControl>
                          <Input placeholder="输入备注信息" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button 
                      type="submit" 
                      disabled={createAccountQuotaMutation.isPending || createTrafficQuotaMutation.isPending}
                    >
                      {(createAccountQuotaMutation.isPending || createTrafficQuotaMutation.isPending) ? '创建中...' : '创建订单'}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总订单数</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orders.length}</div>
            <p className="text-xs text-muted-foreground">
              本月新增 {orders.filter(o => o.createdAt > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length} 个
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">账号配额订单</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {orders.filter(o => o.quotaType === 'ACCOUNT').length}
            </div>
            <p className="text-xs text-muted-foreground">
              总计 {orders.filter(o => o.quotaType === 'ACCOUNT').reduce((sum, o) => sum + o.quotaAmount, 0)} 个账号
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">流量配额订单</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {orders.filter(o => o.quotaType === 'TRAFFIC').length}
            </div>
            <p className="text-xs text-muted-foreground">
              总计 {orders.filter(o => o.quotaType === 'TRAFFIC').reduce((sum, o) => sum + o.quotaAmount, 0)} GB
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 订单列表 */}
      <Card>
        <CardHeader>
          <CardTitle>配额订单列表</CardTitle>
          <CardDescription>
            查看和管理所有配额订单
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索订单号或应用名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  类型: {quotaTypeFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>配额类型</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setQuotaTypeFilter("全部")}>
                  全部
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setQuotaTypeFilter("ACCOUNT")}>
                  账号配额
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setQuotaTypeFilter("TRAFFIC")}>
                  流量配额
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {isLoading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">加载中...</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>订单信息</TableHead>
                  <TableHead>应用</TableHead>
                  <TableHead>配额类型</TableHead>
                  <TableHead>配额数量</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{order.orderNo}</div>
                        <div className="text-sm text-muted-foreground">
                          {order.remarks || '无备注'}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{order.applicationName}</div>
                        <div className="text-sm text-muted-foreground">
                          {order.applicationId}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={order.quotaType === 'ACCOUNT' ? 'default' : 'secondary'}>
                        {order.quotaType === 'ACCOUNT' ? '账号配额' : '流量配额'}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      {order.quotaAmount} {order.quotaType === 'ACCOUNT' ? '个' : 'GB'}
                    </TableCell>
                    <TableCell>
                      {order.createdAt.toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Badge variant="default">已完成</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">打开菜单</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {filteredOrders.length === 0 && !isLoading && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">没有找到匹配的订单</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
