import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@coozf/ui/components/table'
import { Progress } from '@coozf/ui/components/progress'
import {
  Activity,
  Users,
  HardDrive,
  Calendar,
  TrendingUp,
  ArrowUpRight,
  RefreshCw,
  Plus,
} from 'lucide-react'
import { trpc } from '@/lib/trpc'

export const Route = createFileRoute('/_authenticated/')({
  component: DashboardPage,
})

function DashboardPage() {
  // 获取配额概览数据
  const { data: quotaOverview, isLoading, refetch } = trpc.quota.getAllApplicationsQuotaOverview.useQuery()

  // 获取最近的配额订单
  const { data: recentOrders } = trpc.quota.getAllQuotaOrders.useQuery({
    page: 1,
    pageSize: 5,
  })

  // 模拟数据，实际应该从API获取
  const mockApplications = [
    {
      id: 'app-001',
      name: '小红书爬虫应用',
      accountQuota: 100,
      accountUsed: 75,
      accountExpireDate: new Date('2024-03-15'),
      trafficQuotaGB: 500,
      trafficUsedGB: 320,
      status: 'active',
    },
    {
      id: 'app-002',
      name: '抖音数据分析',
      accountQuota: 50,
      accountUsed: 45,
      accountExpireDate: new Date('2024-02-28'),
      trafficQuotaGB: 200,
      trafficUsedGB: 180,
      status: 'warning',
    },
    {
      id: 'app-003',
      name: 'B站内容监控',
      accountQuota: 200,
      accountUsed: 120,
      accountExpireDate: new Date('2024-04-10'),
      trafficQuotaGB: 1000,
      trafficUsedGB: 450,
      status: 'active',
    },
  ]

  const applications = quotaOverview || mockApplications

  // 计算总体统计
  const totalAccountQuota = applications.reduce((sum, app) => sum + app.accountQuota, 0)
  const totalAccountUsed = applications.reduce((sum, app) => sum + app.accountUsed, 0)
  const totalTrafficQuota = applications.reduce((sum, app) => sum + app.trafficQuotaGB, 0)
  const totalTrafficUsed = applications.reduce((sum, app) => sum + app.trafficUsedGB, 0)
  const totalApplications = applications.length

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">管理仪表板</h2>
          <p className="text-muted-foreground">
            配额管理系统概览
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新数据
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            创建配额订单
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground flex items-center">
                {stat.trend === "up" ? (
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                )}
                {stat.change}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-4 lg:grid-cols-7">
        {/* 收入趋势图 */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>收入趋势</CardTitle>
            <CardDescription>
              过去7个月的收入和订单趋势
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <ResponsiveContainer width="100%" height={350}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="收入" 
                  stroke="hsl(var(--primary))" 
                  strokeWidth={2} 
                />
                <Line 
                  type="monotone" 
                  dataKey="订单" 
                  stroke="hsl(var(--chart-2))" 
                  strokeWidth={2} 
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 最近销售 */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>最近销售</CardTitle>
            <CardDescription>
              本月您获得了265笔销售。
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              {recentSales.map((sale, index) => (
                <div key={index} className="flex items-center">
                  <div className="space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {sale.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {sale.email}
                    </p>
                  </div>
                  <div className="ml-auto font-medium">
                    {sale.amount}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 lg:grid-cols-2">
        {/* 订单概览柱状图 */}
        <Card>
          <CardHeader>
            <CardTitle>订单概览</CardTitle>
            <CardDescription>
              每月订单数量统计
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={350}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="订单" fill="hsl(var(--primary))" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 最近订单 */}
        <Card>
          <CardHeader>
            <CardTitle>最近订单</CardTitle>
            <CardDescription>
              您的最新订单列表
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>订单</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>金额</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{order.customer}</div>
                        <div className="text-sm text-muted-foreground">
                          {order.product}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={
                          order.status === "已完成" ? "default" : 
                          order.status === "处理中" ? "secondary" : 
                          "destructive"
                        }
                      >
                        {order.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">{order.amount}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
          <CardDescription>
            常用功能快速入口
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 md:grid-cols-3 lg:grid-cols-6">
            <Button variant="outline" className="justify-start">
              <Users className="mr-2 h-4 w-4" />
              用户管理
            </Button>
            <Button variant="outline" className="justify-start">
              <CreditCard className="mr-2 h-4 w-4" />
              订单管理
            </Button>
            <Button variant="outline" className="justify-start">
              <Activity className="mr-2 h-4 w-4" />
              系统监控
            </Button>
            <Button variant="outline" className="justify-start">
              <DollarSign className="mr-2 h-4 w-4" />
              财务报表
            </Button>
            <Button variant="outline" className="justify-start">
              <TrendingUp className="mr-2 h-4 w-4" />
              数据分析
            </Button>
            <Button variant="outline" className="justify-start">
              配置设置
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
