import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@coozf/ui/components/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@coozf/ui/components/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@coozf/ui/components/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@coozf/ui/components/select'
import {
  Search,
  MoreHorizontal,
  Download,
  Eye,
  RefreshCw,
  Filter,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  Plus,
  CreditCard,
} from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { toast } from 'sonner'
import { useTRPC } from '@/lib/trpc'
import { useQuery } from '@tanstack/react-query'

export const Route = createFileRoute('/_authenticated/orders')({
  component: OrdersPage,
})

// 创建订单表单 schema
const createOrderSchema = z.object({
  applicationId: z.string().min(1, '请选择应用'),
  quotaType: z.enum(['ACCOUNT', 'TRAFFIC'], {
    required_error: '请选择配额类型',
  }),
  quotaAmount: z.number().min(0.01, '配额数量必须大于0'),
  amount: z.number().min(0.01, '金额必须大于0'),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

type CreateOrderForm = z.infer<typeof createOrderSchema>

function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('全部')
  const [page, setPage] = useState(1)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  const trpc = useTRPC()

  // 获取订单列表
  const { data: ordersData, isLoading, refetch } = trpc.order.list.useQuery({
    page,
    pageSize: 20,
    search: searchTerm || undefined,
    status: statusFilter !== '全部' ? (statusFilter as 'PENDING' | 'COMPLETED' | 'CANCELLED') : undefined,
  })

  // 获取应用列表用于创建订单
  const { data: applicationsData } = trpc.application.list.useQuery({
    page: 1,
    pageSize: 100,
  })

  // 创建订单
  const createOrderMutation = trpc.order.create.useMutation({
    onSuccess: () => {
      toast.success('订单创建成功')
      setIsCreateDialogOpen(false)
      refetch()
      createForm.reset()
    },
    onError: (error) => {
      toast.error(error.message || '创建失败')
    },
  })

  // 开通转账
  const bankTransferMutation = trpc.order.bankTransfer.useMutation({
    onSuccess: () => {
      toast.success('开通转账成功')
      refetch()
    },
    onError: (error) => {
      toast.error(error.message || '操作失败')
    },
  })

  // 取消订单
  const cancelOrderMutation = trpc.order.cancelOrder.useMutation({
    onSuccess: () => {
      toast.success('订单已取消')
      refetch()
    },
    onError: (error) => {
      toast.error(error.message || '操作失败')
    },
  })

  // 创建订单表单
  const createForm = useForm<CreateOrderForm>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      applicationId: '',
      quotaType: 'ACCOUNT',
      quotaAmount: 0,
      amount: 0,
      remarks: '',
    },
  })

  const onCreateSubmit = (values: CreateOrderForm) => {
    createOrderMutation.mutate(values)
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'default'
      case 'PENDING':
        return 'secondary'
      case 'CANCELLED':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'PENDING':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-orange-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return '已完成'
      case 'PENDING':
        return '待处理'
      case 'CANCELLED':
        return '已取消'
      default:
        return status
    }
  }

  const getQuotaTypeText = (quotaType: string) => {
    switch (quotaType) {
      case 'ACCOUNT':
        return '账号配额'
      case 'TRAFFIC':
        return '流量配额'
      default:
        return quotaType
    }
  }

  // 计算统计数据
  const orders = ordersData?.items || []
  const totalOrders = ordersData?.total || 0
  const completedOrders = orders.filter((o) => o.status === 'COMPLETED').length
  const pendingOrders = orders.filter((o) => o.status === 'PENDING').length
  const cancelledOrders = orders.filter((o) => o.status === 'CANCELLED').length
  const totalRevenue = orders
    .filter((o) => o.status === 'COMPLETED')
    .reduce((sum, o) => sum + Number(o.amount), 0)

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">订单管理</h2>
          <p className="text-muted-foreground">查看和管理所有订单信息</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出订单
          </Button>
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总订单数</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalOrders}</div>
            <p className="text-xs text-muted-foreground">+3 从昨天</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedOrders}</div>
            <p className="text-xs text-muted-foreground">完成率 {Math.round((completedOrders / totalOrders) * 100)}%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">处理中</CardTitle>
            <Loader2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{processingOrders}</div>
            <p className="text-xs text-muted-foreground">需要关注</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">{pendingPayment} 个待支付</p>
          </CardContent>
        </Card>
      </div>

      {/* 订单列表 */}
      <Card>
        <CardHeader>
          <CardTitle>订单列表</CardTitle>
          <CardDescription>查看和管理所有订单</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索订单号、客户或服务..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  状态: {statusFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>订单状态</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('全部')}>全部</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('已完成')}>已完成</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('处理中')}>处理中</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('已取消')}>已取消</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('待支付')}>待支付</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  支付: {paymentFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>支付状态</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setPaymentFilter('全部')}>全部</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setPaymentFilter('已支付')}>已支付</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setPaymentFilter('待支付')}>待支付</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setPaymentFilter('已退款')}>已退款</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>订单信息</TableHead>
                <TableHead>客户</TableHead>
                <TableHead>服务</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>订单状态</TableHead>
                <TableHead>支付状态</TableHead>
                <TableHead>使用情况</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{order.id}</div>
                      <div className="text-sm text-muted-foreground">{order.createdAt}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{order.customer}</div>
                      <div className="text-sm text-muted-foreground">{order.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{order.service}</div>
                  </TableCell>
                  <TableCell className="font-medium">{order.amount}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(order.status)}
                      <Badge variant={getStatusBadgeVariant(order.status)}>{order.status}</Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getPaymentStatusBadgeVariant(order.paymentStatus)}>{order.paymentStatus}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>
                        {order.calls} / {order.quota} 次
                      </div>
                      <div className="text-muted-foreground">{Math.round((order.calls / order.quota) * 100)}% 已用</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">打开菜单</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          刷新状态
                        </DropdownMenuItem>
                        {order.status === '待支付' && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <XCircle className="mr-2 h-4 w-4" />
                              取消订单
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredOrders.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">没有找到匹配的订单</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
