import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@coozf/ui/components/dropdown-menu'
import {
  Search,
  MoreHorizontal,
  Download,
  Eye,
  RefreshCw,
  Filter,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
} from 'lucide-react'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useTRPC } from '@/lib/trpc'

export const Route = createFileRoute('/_authenticated/orders')({
  component: OrdersPage,
})

// 模拟订单数据
const orders = [
  {
    id: 'ORD-2024-001',
    customer: '张三',
    email: '<EMAIL>',
    service: '小红书API套餐',
    amount: '¥299.00',
    status: '已完成',
    paymentStatus: '已支付',
    createdAt: '2024-01-15 14:30:25',
    completedAt: '2024-01-15 14:35:10',
    calls: 1250,
    quota: 2000,
  },
  {
    id: 'ORD-2024-002',
    customer: '李四',
    email: '<EMAIL>',
    service: '抖音API套餐',
    amount: '¥199.00',
    status: '处理中',
    paymentStatus: '已支付',
    createdAt: '2024-01-15 10:22:15',
    completedAt: null,
    calls: 0,
    quota: 1000,
  },
  {
    id: 'ORD-2024-003',
    customer: '王五',
    email: '<EMAIL>',
    service: '快手API套餐',
    amount: '¥399.00',
    status: '已取消',
    paymentStatus: '已退款',
    createdAt: '2024-01-14 16:45:30',
    completedAt: null,
    calls: 0,
    quota: 3000,
  },
  {
    id: 'ORD-2024-004',
    customer: '赵六',
    email: '<EMAIL>',
    service: '全平台API套餐',
    amount: '¥899.00',
    status: '已完成',
    paymentStatus: '已支付',
    createdAt: '2024-01-14 11:15:45',
    completedAt: '2024-01-14 11:20:12',
    calls: 5420,
    quota: 10000,
  },
  {
    id: 'ORD-2024-005',
    customer: '钱七',
    email: '<EMAIL>',
    service: 'B站API套餐',
    amount: '¥159.00',
    status: '待支付',
    paymentStatus: '待支付',
    createdAt: '2024-01-13 09:30:18',
    completedAt: null,
    calls: 0,
    quota: 500,
  },
]

function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('全部')
  const [paymentFilter, setPaymentFilter] = useState('全部')

  const trpc = useTRPC()

  const list = useQuery(
    trpc.order.list.queryOptions({
      page: 1,
      pageSize: 20,
    }),
  )

  const filteredOrders = orders.filter((order) => {
    const matchesSearch =
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.service.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === '全部' || order.status === statusFilter
    const matchesPayment = paymentFilter === '全部' || order.paymentStatus === paymentFilter
    return matchesSearch && matchesStatus && matchesPayment
  })

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case '已完成':
        return 'default'
      case '处理中':
        return 'secondary'
      case '已取消':
        return 'destructive'
      case '待支付':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const getPaymentStatusBadgeVariant = (status: string) => {
    switch (status) {
      case '已支付':
        return 'default'
      case '待支付':
        return 'destructive'
      case '已退款':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case '已完成':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case '处理中':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case '已取消':
        return <XCircle className="h-4 w-4 text-red-500" />
      case '待支付':
        return <Clock className="h-4 w-4 text-orange-500" />
      default:
        return null
    }
  }

  // 计算统计数据
  const totalOrders = orders.length
  const completedOrders = orders.filter((o) => o.status === '已完成').length
  const processingOrders = orders.filter((o) => o.status === '处理中').length
  const pendingPayment = orders.filter((o) => o.paymentStatus === '待支付').length
  const totalRevenue = orders
    .filter((o) => o.paymentStatus === '已支付')
    .reduce((sum, o) => sum + parseFloat(o.amount.replace('¥', '').replace(',', '')), 0)

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">订单管理</h2>
          <p className="text-muted-foreground">查看和管理所有订单信息</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出订单
          </Button>
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总订单数</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalOrders}</div>
            <p className="text-xs text-muted-foreground">+3 从昨天</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedOrders}</div>
            <p className="text-xs text-muted-foreground">完成率 {Math.round((completedOrders / totalOrders) * 100)}%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">处理中</CardTitle>
            <Loader2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{processingOrders}</div>
            <p className="text-xs text-muted-foreground">需要关注</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">{pendingPayment} 个待支付</p>
          </CardContent>
        </Card>
      </div>

      {/* 订单列表 */}
      <Card>
        <CardHeader>
          <CardTitle>订单列表</CardTitle>
          <CardDescription>查看和管理所有订单</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索订单号、客户或服务..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  状态: {statusFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>订单状态</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('全部')}>全部</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('已完成')}>已完成</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('处理中')}>处理中</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('已取消')}>已取消</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('待支付')}>待支付</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  支付: {paymentFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>支付状态</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setPaymentFilter('全部')}>全部</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setPaymentFilter('已支付')}>已支付</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setPaymentFilter('待支付')}>待支付</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setPaymentFilter('已退款')}>已退款</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>订单信息</TableHead>
                <TableHead>客户</TableHead>
                <TableHead>服务</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>订单状态</TableHead>
                <TableHead>支付状态</TableHead>
                <TableHead>使用情况</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{order.id}</div>
                      <div className="text-sm text-muted-foreground">{order.createdAt}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{order.customer}</div>
                      <div className="text-sm text-muted-foreground">{order.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{order.service}</div>
                  </TableCell>
                  <TableCell className="font-medium">{order.amount}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(order.status)}
                      <Badge variant={getStatusBadgeVariant(order.status)}>{order.status}</Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getPaymentStatusBadgeVariant(order.paymentStatus)}>{order.paymentStatus}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>
                        {order.calls} / {order.quota} 次
                      </div>
                      <div className="text-muted-foreground">{Math.round((order.calls / order.quota) * 100)}% 已用</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">打开菜单</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          刷新状态
                        </DropdownMenuItem>
                        {order.status === '待支付' && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <XCircle className="mr-2 h-4 w-4" />
                              取消订单
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredOrders.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">没有找到匹配的订单</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
