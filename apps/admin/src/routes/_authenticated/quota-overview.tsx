import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@coozf/ui/components/table'
import { Progress } from '@coozf/ui/components/progress'
import { 
  Activity, 
  Users, 
  HardDrive,
  Calendar,
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  Plus,
  RefreshCw,
} from 'lucide-react'
import { trpc } from '@/lib/trpc'

export const Route = createFileRoute('/_authenticated/quota-overview')({
  component: QuotaOverviewPage,
})

function QuotaOverviewPage() {
  // 获取所有应用的配额概览
  const { data: quotaOverview, isLoading, refetch } = trpc.quota.getAllApplicationsQuotaOverview.useQuery()

  // 模拟数据，实际应该从API获取
  const mockApplications = [
    {
      id: 'app-001',
      name: '小红书爬虫应用',
      accountQuota: 100,
      accountUsed: 75,
      accountExpireDate: new Date('2024-03-15'),
      trafficQuotaGB: 500,
      trafficUsedGB: 320,
      status: 'active',
    },
    {
      id: 'app-002', 
      name: '抖音数据分析',
      accountQuota: 50,
      accountUsed: 45,
      accountExpireDate: new Date('2024-02-28'),
      trafficQuotaGB: 200,
      trafficUsedGB: 180,
      status: 'warning',
    },
    {
      id: 'app-003',
      name: 'B站内容监控',
      accountQuota: 200,
      accountUsed: 120,
      accountExpireDate: new Date('2024-04-10'),
      trafficQuotaGB: 1000,
      trafficUsedGB: 450,
      status: 'active',
    },
  ]

  const applications = quotaOverview || mockApplications

  // 计算总体统计
  const totalAccountQuota = applications.reduce((sum, app) => sum + app.accountQuota, 0)
  const totalAccountUsed = applications.reduce((sum, app) => sum + app.accountUsed, 0)
  const totalTrafficQuota = applications.reduce((sum, app) => sum + app.trafficQuotaGB, 0)
  const totalTrafficUsed = applications.reduce((sum, app) => sum + app.trafficUsedGB, 0)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default">正常</Badge>
      case 'warning':
        return <Badge variant="destructive">警告</Badge>
      case 'expired':
        return <Badge variant="secondary">已过期</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const getAccountUsageColor = (used: number, total: number) => {
    const percentage = (used / total) * 100
    if (percentage >= 90) return 'text-red-500'
    if (percentage >= 70) return 'text-yellow-500'
    return 'text-green-500'
  }

  const getTrafficUsageColor = (used: number, total: number) => {
    const percentage = (used / total) * 100
    if (percentage >= 90) return 'text-red-500'
    if (percentage >= 70) return 'text-yellow-500'
    return 'text-green-500'
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">配额概览</h2>
          <p className="text-muted-foreground">
            查看所有应用的配额使用情况和状态
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            创建配额订单
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总账号配额</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAccountQuota}</div>
            <p className="text-xs text-muted-foreground">
              已使用 {totalAccountUsed} 个
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">账号使用率</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round((totalAccountUsed / totalAccountQuota) * 100)}%
            </div>
            <Progress 
              value={(totalAccountUsed / totalAccountQuota) * 100} 
              className="mt-2"
            />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总流量配额</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTrafficQuota} GB</div>
            <p className="text-xs text-muted-foreground">
              已使用 {totalTrafficUsed} GB
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">流量使用率</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round((totalTrafficUsed / totalTrafficQuota) * 100)}%
            </div>
            <Progress 
              value={(totalTrafficUsed / totalTrafficQuota) * 100} 
              className="mt-2"
            />
          </CardContent>
        </Card>
      </div>

      {/* 应用配额详情表格 */}
      <Card>
        <CardHeader>
          <CardTitle>应用配额详情</CardTitle>
          <CardDescription>
            查看每个应用的配额分配和使用情况
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">加载中...</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>应用名称</TableHead>
                  <TableHead>账号配额</TableHead>
                  <TableHead>账号使用率</TableHead>
                  <TableHead>到期时间</TableHead>
                  <TableHead>流量配额</TableHead>
                  <TableHead>流量使用率</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {applications.map((app) => (
                  <TableRow key={app.id}>
                    <TableCell>
                      <div className="font-medium">{app.name}</div>
                      <div className="text-sm text-muted-foreground">{app.id}</div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{app.accountUsed} / {app.accountQuota}</div>
                    </TableCell>
                    <TableCell>
                      <div className={`font-medium ${getAccountUsageColor(app.accountUsed, app.accountQuota)}`}>
                        {Math.round((app.accountUsed / app.accountQuota) * 100)}%
                      </div>
                      <Progress 
                        value={(app.accountUsed / app.accountQuota) * 100} 
                        className="mt-1 w-16"
                      />
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {app.accountExpireDate.toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{app.trafficUsedGB} / {app.trafficQuotaGB} GB</div>
                    </TableCell>
                    <TableCell>
                      <div className={`font-medium ${getTrafficUsageColor(app.trafficUsedGB, app.trafficQuotaGB)}`}>
                        {Math.round((app.trafficUsedGB / app.trafficQuotaGB) * 100)}%
                      </div>
                      <Progress 
                        value={(app.trafficUsedGB / app.trafficQuotaGB) * 100} 
                        className="mt-1 w-16"
                      />
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(app.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm">
                        管理配额
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
