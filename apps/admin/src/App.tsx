import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { RouterProvider } from '@tanstack/react-router'
import { AuthProvider } from './lib/auth/auth-provider'
import { useAuth } from './lib/auth/auth-context'
import { router } from './router'
import './App.css'
import { useCallback, useMemo, useState } from 'react'
import { createTRPCClient, httpLink } from '@trpc/client'
import superjson from 'superjson'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@coozf/ui/components/sonner'
import { TRPCClientError } from '@trpc/client'
import type { AppRouter } from '@server/router'
import { toast } from 'sonner'
import { TRPCProvider } from './lib/trpc'

function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1000,
        retry: false,
        throwOnError: (error) => {
          console.log('query error', error)
          handleError(error as TRPCClientError<AppRouter>)
          return false
        },
      },
    },
  })
}
let browserQueryClient: QueryClient | undefined = undefined

function getQueryClient() {
  if (typeof window === 'undefined') {
    // Server: always make a new query client
    return makeQueryClient()
  } else {
    // Browser: make a new query client if we don't already have one
    // This is very important, so we don't re-make a new client if React
    // suspends during the initial render. This may not be needed if we
    // have a suspense boundary BELOW the creation of the query client
    if (!browserQueryClient) browserQueryClient = makeQueryClient()
    return browserQueryClient
  }
}

// 统一的错误处理函数
const handleError = (error: TRPCClientError<AppRouter>) => {
  // 可以在这里添加错误日志上报
  console.error('API Error:', error)

  // 处理特定类型的错误
  if (error instanceof TRPCClientError) {
    if (error.data?.httpStatus === 401) {
      toast.error('登录已过期，请重新登录')
      // 可以在这里触发登出逻辑
      
      return
    }

    if (error.message.includes('NETWORK_ERROR')) {
      toast.error('网络连接错误，请检查网络后重试')
      return
    }
  }

  // 默认错误提示
  toast.error(error.message || '操作失败，请稍后重试')
}

function InnerApp() {
  const auth = useAuth()
  const queryClient = useMemo(() => getQueryClient(), [])
  const [trpcClient] = useState(() =>
    // @ts-ignore
    createTRPCClient<AdminRouter>({
      links: [
        httpLink({
          url: import.meta.env.VITE_API_BASE_URL + '/api/trpc',
          transformer: superjson,
        }),
      ],
    }),
  )

  return (
    <QueryClientProvider client={queryClient}>
      <TRPCProvider trpcClient={trpcClient} queryClient={queryClient}>
        <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
          <RouterProvider router={router} context={{ auth }} />
          <Toaster position="top-center" richColors />
        </ThemeProvider>
      </TRPCProvider>
    </QueryClientProvider>
  )
}

function App() {
  return (
    <AuthProvider>
      <InnerApp />
    </AuthProvider>
  )
}

export default App
