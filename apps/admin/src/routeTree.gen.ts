/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated/index'
import { Route as AuthenticatedUsersRouteImport } from './routes/_authenticated/users'
import { Route as AuthenticatedQuotaOverviewRouteImport } from './routes/_authenticated/quota-overview'
import { Route as AuthenticatedQuotaOrdersRouteImport } from './routes/_authenticated/quota-orders'
import { Route as AuthenticatedOrdersRouteImport } from './routes/_authenticated/orders'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedUsersRoute = AuthenticatedUsersRouteImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedQuotaOverviewRoute =
  AuthenticatedQuotaOverviewRouteImport.update({
    id: '/quota-overview',
    path: '/quota-overview',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedQuotaOrdersRoute =
  AuthenticatedQuotaOrdersRouteImport.update({
    id: '/quota-orders',
    path: '/quota-orders',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedOrdersRoute = AuthenticatedOrdersRouteImport.update({
  id: '/orders',
  path: '/orders',
  getParentRoute: () => AuthenticatedRoute,
} as any)

export interface FileRoutesByFullPath {
  '/login': typeof LoginRoute
  '/orders': typeof AuthenticatedOrdersRoute
  '/quota-orders': typeof AuthenticatedQuotaOrdersRoute
  '/quota-overview': typeof AuthenticatedQuotaOverviewRoute
  '/users': typeof AuthenticatedUsersRoute
  '/': typeof AuthenticatedIndexRoute
}
export interface FileRoutesByTo {
  '/login': typeof LoginRoute
  '/orders': typeof AuthenticatedOrdersRoute
  '/quota-orders': typeof AuthenticatedQuotaOrdersRoute
  '/quota-overview': typeof AuthenticatedQuotaOverviewRoute
  '/users': typeof AuthenticatedUsersRoute
  '/': typeof AuthenticatedIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/_authenticated/orders': typeof AuthenticatedOrdersRoute
  '/_authenticated/quota-orders': typeof AuthenticatedQuotaOrdersRoute
  '/_authenticated/quota-overview': typeof AuthenticatedQuotaOverviewRoute
  '/_authenticated/users': typeof AuthenticatedUsersRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/login'
    | '/orders'
    | '/quota-orders'
    | '/quota-overview'
    | '/users'
    | '/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/orders'
    | '/quota-orders'
    | '/quota-overview'
    | '/users'
    | '/'
  id:
    | '__root__'
    | '/_authenticated'
    | '/login'
    | '/_authenticated/orders'
    | '/_authenticated/quota-orders'
    | '/_authenticated/quota-overview'
    | '/_authenticated/users'
    | '/_authenticated/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/users': {
      id: '/_authenticated/users'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof AuthenticatedUsersRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/quota-overview': {
      id: '/_authenticated/quota-overview'
      path: '/quota-overview'
      fullPath: '/quota-overview'
      preLoaderRoute: typeof AuthenticatedQuotaOverviewRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/quota-orders': {
      id: '/_authenticated/quota-orders'
      path: '/quota-orders'
      fullPath: '/quota-orders'
      preLoaderRoute: typeof AuthenticatedQuotaOrdersRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/orders': {
      id: '/_authenticated/orders'
      path: '/orders'
      fullPath: '/orders'
      preLoaderRoute: typeof AuthenticatedOrdersRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedOrdersRoute: typeof AuthenticatedOrdersRoute
  AuthenticatedQuotaOrdersRoute: typeof AuthenticatedQuotaOrdersRoute
  AuthenticatedQuotaOverviewRoute: typeof AuthenticatedQuotaOverviewRoute
  AuthenticatedUsersRoute: typeof AuthenticatedUsersRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedOrdersRoute: AuthenticatedOrdersRoute,
  AuthenticatedQuotaOrdersRoute: AuthenticatedQuotaOrdersRoute,
  AuthenticatedQuotaOverviewRoute: AuthenticatedQuotaOverviewRoute,
  AuthenticatedUsersRoute: AuthenticatedUsersRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
