import { fromNodeHeaders } from "better-auth/node"
import { FastifyRequest } from "fastify"
import { auth } from "./auth"
import { db } from "@coozf/db"
import { authAdmin } from ".."

export const getUser = async (req: FastifyRequest) => {
    const headers = fromNodeHeaders(req.headers)
    const data = await auth.api.getSession({
      headers, //some endpoint might require headers
    })
  
    if (data) {
      return data.user
    }
    return null
}

export const getAdminUser = async (req: FastifyRequest) => {
  const headers = fromNodeHeaders(req.headers)
  const data = await authAdmin.api.getSession({
    headers, //some endpoint might require headers
  })

  if (data) { 
    // try {
    //   const user = await db.admin_user.findUnique({ where: { id: data.user.id } })
    //   if (!user) throw new Error("User not found")

    //   return user
    //   } catch (error) {
    //   console.log("error", error)
    //   return null
    // }
    return data.user
  }
  return null
}