{"version": "0.2.0", "configurations": [{"name": "🚀 启动服务端", "type": "node", "request": "launch", "runtimeExecutable": "npx", "runtimeArgs": ["tsx", "watch"], "args": ["src/index.ts"], "cwd": "${workspaceFolder}/apps/server", "env": {"NODE_ENV": "dev"}, "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "restart": true, "envFile": "${workspaceFolder}/apps/server/.env"}, {"name": "🐛 调试服务端", "type": "node", "request": "launch", "runtimeExecutable": "pnpm", "runtimeArgs": ["run"], "args": ["dev"], "cwd": "${workspaceFolder}/apps/server", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "envFile": "${workspaceFolder}/apps/server/.env", "stopOnEntry": false}, {"name": "🌐 启动客户端", "type": "node", "request": "launch", "runtimeExecutable": "npx", "runtimeArgs": ["vite"], "args": ["--mode", "dev"], "cwd": "${workspaceFolder}/client", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "envFile": "${workspaceFolder}/client/.env.dev"}, {"name": "🔍 附加到 Chrome (客户端)", "type": "chrome", "request": "attach", "port": 9222, "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/client/src", "sourceMaps": true, "sourceMapPathOverrides": {"webpack:///./src/*": "${workspaceFolder}/client/src/*"}}, {"name": "🖥️ 打开 Chrome (客户端)", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/client/src", "sourceMaps": true, "userDataDir": false}, {"name": "🧪 运行 E2E 测试", "type": "node", "request": "launch", "runtimeExecutable": "npx", "runtimeArgs": ["playwright"], "args": ["test"], "cwd": "${workspaceFolder}/tests-e2e", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}], "compounds": [{"name": "🔥 全栈开发 (客户端 + 服务端)", "configurations": ["🚀 启动服务端", "🌐 启动客户端"], "presentation": {"hidden": false, "group": "dev", "order": 1}, "stopAll": true}, {"name": "🐛 全栈调试 (附加 Chrome)", "configurations": ["🐛 调试服务端", "🔍 附加到 Chrome (客户端)"], "presentation": {"hidden": false, "group": "debug", "order": 2}}]}